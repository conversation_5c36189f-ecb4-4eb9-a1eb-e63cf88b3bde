package cz.deuss.userservice.config

import cz.deuss.userservice.database.repository.InviteTokenRepository
import cz.deuss.userservice.service.InviteTokenValidator
import cz.deuss.userservice.service.LocalDevelopmentInviteTokenValidator
import cz.deuss.userservice.service.ProductionInviteTokenValidator
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton

/**
 * Configuration factory for InviteTokenValidator implementations.
 * Chooses between production and local development implementations based on configuration.
 */
@Factory
class InviteTokenConfiguration {

    @Bean
    @Singleton
    fun inviteTokenValidator(
        @Value("\${micronaut.security.token.invite-token.generator.enable}")
        inviteTokenEnabled: <PERSON><PERSON>an,
        inviteTokenRepository: InviteTokenRepository,
    ): InviteTokenValidator {
        return if (inviteTokenEnabled) {
            ProductionInviteTokenValidator(inviteTokenRepository)
        } else {
            LocalDevelopmentInviteTokenValidator()
        }
    }
}
