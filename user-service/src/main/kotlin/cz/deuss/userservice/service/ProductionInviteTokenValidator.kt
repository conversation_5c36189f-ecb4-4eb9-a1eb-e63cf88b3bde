package cz.deuss.userservice.service

import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.database.model.InviteToken
import cz.deuss.userservice.database.repository.InviteTokenRepository
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

/**
 * Production implementation of InviteTokenValidator.
 * Performs full validation against the database.
 */
open class ProductionInviteTokenValidator(
    private val inviteTokenRepository: InviteTokenRepository,
) : InviteTokenValidator {

    override fun findAndValidateToken(tokenId: UUID): InviteToken {
        val token = inviteTokenRepository.findById(tokenId).getOrNull()

        if (token == null || token.hasExpired() || token.registeredUserId != null || token.revoked) {
            throw NotFoundException("InviteToken#${tokenId} is invalid.")
        }

        return token
    }

    @Transactional
    open override fun useToken(tokenId: UUID, userId: UUID) {
        findAndValidateToken(tokenId)
        inviteTokenRepository.updateRegisteredUserIdById(tokenId, userId)
    }

    override fun verifyToken(tokenId: UUID) {
        findAndValidateToken(tokenId)
    }
}
