package cz.deuss.userservice.service

import cz.deuss.userservice.database.model.InviteToken
import jakarta.inject.Singleton
import java.time.LocalDateTime
import java.util.UUID

/**
 * Local development implementation of InviteTokenValidator.
 * Bypasses all validation to allow development without requiring valid invite tokens.
 */
class LocalDevelopmentInviteTokenValidator : InviteTokenValidator {

    override fun findAndValidateToken(tokenId: UUID): InviteToken {
        // Return a dummy token that appears valid for local development
        return InviteToken(
            createdByUser = null,
            expiresAt = LocalDateTime.now().plusDays(1)
        )
    }

    override fun useToken(tokenId: UUID, userId: UUID) {
        // No-op for local development - skip token usage tracking
    }

    override fun verifyToken(tokenId: UUID) {
        // Always succeed for local development
    }
}
