micronaut:
  server:
    port: 8080
  security:
    intercept-url-map:
      - pattern: /api/exposed/openapi/**
        access: isAnonymous()
      - pattern: /api/exposed/**
        access: isAuthenticated()
  router:
    static-resources:
      openapi-yaml:
        paths: classpath:openapi
        mapping: /api/exposed/openapi/**
passkey:
  origin: ${PASSKEY_ORIGIN:`https://dev.deussblockchain.eu`} # Address of dev frontend
