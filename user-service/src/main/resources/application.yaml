micronaut:
  application:
    name: user-service
  server:
    port: 8080
    netty:
      access-logger:
        enabled: true
  security:
    token:
      invite-token:
        generator:
          expiration: ${JWT_GENERATOR_INVITE_TOKEN_EXPIRY:PT48H} # 48 hours
      jwt:
        generator:
          refresh-token:
            enabled: true
            secret: ${JWT_GENERATOR_SIGNATURE_SECRET}
            expiration: ${JWT_GENERATOR_REFRESH_TOKEN_EXPIRY:2592000} # 30 days
            jws-algorithm: HS256

jpa:
  default:
    entity-scan:
      packages:
        - 'cz.deuss.userservice.database.model'
        - 'cz.deuss.userservice.user.role.repository'
    properties:
      hibernate:
        show_sql: true
flyway:
  datasources:
    default:
      enabled: true
      locations: db/migration
datasources:
  default:
    db-type: postgres
    dialect: POSTGRES
    driver-class-name: org.postgresql.Driver
    url: ${DATABASE_URL:`************************************************`}
    username: ${DATABASE_USER:user_service_user}
    password: ${DATABASE_PASSWORD:user_service}

mailing:
  port: ${SMTP_PORT:587}
  host: ${SMTP_HOST:relay.feld.cvut.cz}
  # in VaultWarden
  username: ${SMTP_USERNAME}
  password: ${SMTP_PASSWORD}
  from-name: ${SMTP_FROM_NAME:`Deuss EU`}
  from-address: ${SMTP_FROM_ADDRESS:`<EMAIL>`}

passkey:
  rp-id: ${PASSKEY_RP_ID:`deussblockchain.eu`}  # Relying Party ID
  rp-name: ${PASSKEY_RP_NAME:`Deuss EU`}
  origin: ${PASSKEY_ORIGIN:`https://deussblockchain.eu`} # Address of the frontend
  challenge-timeout: ${PASSKEY_CHALLENGE_TIMEOUT:PT5M}
  registration:
    default-friendly-name: "Registration"
  additional:
    default-friendly-name: "Additional"
