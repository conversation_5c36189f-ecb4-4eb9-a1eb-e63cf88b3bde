micronaut:
  security:
    token:
      jwt:
        signatures:
          secret:
            generator:
              secret: ${JWT_GENERATOR_SIGNATURE_SECRET:pleaseChangeThisSecretForANewOne}
              jws-algorithm: HS256
        generator:
          refresh-token:
            secret: ${JWT_GENERATOR_SIGNATURE_SECRET:pleaseChangeThisSecretForANewOne}
            jws-algorithm: HS256


datasources:
  default:
    db-type: postgres
    dialect: POSTGRES
    driver-class-name: org.postgresql.Driver
    url: ************************************************
    username: user_service_user
    password: user_service
jpa:
  default:
    properties:
      hibernate:
        show_sql: true
passkey:
  rp-id: 'localhost'  # Relying Party ID
  rp-name: 'Deuss services'
  origin: 'http://localhost:63342' # Address idea uses to serve the frontend. In production:address of the frontend
  challenge-timeout: PT5M

mailing:
  username: ${SMTP_USERNAME:placeholderToNotFailAtStart}
  password: ${SMTP_PASSWORD:placeholderToNotFailAtStart}
